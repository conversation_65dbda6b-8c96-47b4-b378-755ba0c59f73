{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": false, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-router-dom": "^6.25.1", "react-scripts": "5.0.1", "styled-components": "^6.1.12", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss": "^8.4.40", "tailwindcss": "^3.4.7"}}